/usr/bin/c++ -rdynamic CMakeFiles/abot_driver.dir/src/main.cpp.o CMakeFiles/abot_driver.dir/src/base_driver_config.cpp.o CMakeFiles/abot_driver.dir/src/base_driver.cpp.o CMakeFiles/abot_driver.dir/src/data_holder.cpp.o CMakeFiles/abot_driver.dir/src/simple_dataframe_master.cpp.o CMakeFiles/abot_driver.dir/src/serial_transport.cpp.o -o /home/<USER>/310319/devel/lib/abot_bringup/abot_driver  -Wl,-rpath,/opt/ros/melodic/lib /opt/ros/melodic/lib/libtf.so /opt/ros/melodic/lib/libtf2_ros.so /opt/ros/melodic/lib/libactionlib.so /opt/ros/melodic/lib/libmessage_filters.so /opt/ros/melodic/lib/libroscpp.so -lboost_filesystem /opt/ros/melodic/lib/libxmlrpcpp.so /opt/ros/melodic/lib/libtf2.so /opt/ros/melodic/lib/librosconsole.so /opt/ros/melodic/lib/librosconsole_log4cxx.so /opt/ros/melodic/lib/librosconsole_backend_interface.so -llog4cxx -lboost_regex /opt/ros/melodic/lib/libdynamic_reconfigure_config_init_mutex.so /opt/ros/melodic/lib/libroscpp_serialization.so /opt/ros/melodic/lib/librostime.so /opt/ros/melodic/lib/libcpp_common.so -lboost_system -lboost_thread -lboost_chrono -lboost_date_time -lboost_atomic -lpthread /usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4 
