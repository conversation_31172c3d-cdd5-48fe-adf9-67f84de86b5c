# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/310319/devel/include".split(';') if "/home/<USER>/310319/devel/include" != "" else []
PROJECT_CATKIN_DEPENDS = "".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "abot_bringup"
PROJECT_SPACE_DIR = "/home/<USER>/310319/devel"
PROJECT_VERSION = "0.0.0"
