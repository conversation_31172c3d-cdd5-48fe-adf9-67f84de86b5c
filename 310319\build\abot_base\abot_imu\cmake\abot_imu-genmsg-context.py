# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/home/<USER>/310319/src/abot_base/abot_imu/msg/RawImu.msg"
services_str = ""
pkg_name = "abot_imu"
dependencies_str = "std_msgs;geometry_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "abot_imu;/home/<USER>/310319/src/abot_base/abot_imu/msg;std_msgs;/opt/ros/melodic/share/std_msgs/cmake/../msg;geometry_msgs;/opt/ros/melodic/share/geometry_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/usr/bin/python2"
package_has_static_sources = '' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/melodic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
