set(_CATKIN_CURRENT_PACKAGE "abot_model")
set(abot_model_VERSION "1.0.0")
set(abot_model_MAINTAINER " <<EMAIL>>")
set(abot_model_PACKAGE_FORMAT "1")
set(abot_model_BUILD_DEPENDS "roslaunch")
set(abot_model_BUILD_EXPORT_DEPENDS "robot_state_publisher" "rviz" "joint_state_publisher" "gazebo")
set(abot_model_BUILDTOOL_DEPENDS "catkin")
set(abot_model_BUILDTOOL_EXPORT_DEPENDS )
set(abot_model_EXEC_DEPENDS "robot_state_publisher" "rviz" "joint_state_publisher" "gazebo")
set(abot_model_RUN_DEPENDS "robot_state_publisher" "rviz" "joint_state_publisher" "gazebo")
set(abot_model_TEST_DEPENDS )
set(abot_model_DOC_DEPENDS )
set(abot_model_URL_WEBSITE "")
set(abot_model_URL_BUGTRACKER "")
set(abot_model_URL_REPOSITORY "")
set(abot_model_DEPRECATED "")