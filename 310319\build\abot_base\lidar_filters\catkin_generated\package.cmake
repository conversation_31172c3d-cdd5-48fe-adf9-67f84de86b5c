set(_CATKIN_CURRENT_PACKAGE "lidar_filters")
set(lidar_filters_VERSION "0.0.0")
set(lidar_filters_MAINTAINER "abot <<EMAIL>>")
set(lidar_filters_PACKAGE_FORMAT "2")
set(lidar_filters_BUILD_DEPENDS "roscpp" "rospy" "std_msgs")
set(lidar_filters_BUILD_EXPORT_DEPENDS "roscpp" "rospy" "std_msgs")
set(lidar_filters_BUILDTOOL_DEPENDS "catkin")
set(lidar_filters_BUILDTOOL_EXPORT_DEPENDS )
set(lidar_filters_EXEC_DEPENDS "roscpp" "rospy" "std_msgs")
set(lidar_filters_RUN_DEPENDS "roscpp" "rospy" "std_msgs")
set(lidar_filters_TEST_DEPENDS )
set(lidar_filters_DOC_DEPENDS )
set(lidar_filters_URL_WEBSITE "")
set(lidar_filters_URL_BUGTRACKER "")
set(lidar_filters_URL_REPOSITORY "")
set(lidar_filters_DEPRECATED "")