# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/home/<USER>/310319/src/abot_find/msg/ObjectsStamped.msg;/home/<USER>/310319/src/abot_find/msg/DetectionInfo.msg"
services_str = ""
pkg_name = "find_object_2d"
dependencies_str = "std_msgs;sensor_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "find_object_2d;/home/<USER>/310319/src/abot_find/msg;std_msgs;/opt/ros/melodic/share/std_msgs/cmake/../msg;sensor_msgs;/opt/ros/melodic/share/sensor_msgs/cmake/../msg;geometry_msgs;/opt/ros/melodic/share/geometry_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/usr/bin/python2"
package_has_static_sources = '' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/melodic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
