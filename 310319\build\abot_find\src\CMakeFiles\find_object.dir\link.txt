/usr/bin/c++ -fPIC -shared -Wl,-soname,libfind_object_2d.so -o /home/<USER>/310319/devel/lib/libfind_object_2d.so CMakeFiles/find_object.dir/MainWindow.cpp.o CMakeFiles/find_object.dir/AddObjectDialog.cpp.o CMakeFiles/find_object.dir/KeypointItem.cpp.o CMakeFiles/find_object.dir/RectItem.cpp.o CMakeFiles/find_object.dir/QtOpenCV.cpp.o CMakeFiles/find_object.dir/Camera.cpp.o CMakeFiles/find_object.dir/CameraTcpServer.cpp.o CMakeFiles/find_object.dir/ParametersToolBox.cpp.o CMakeFiles/find_object.dir/Settings.cpp.o CMakeFiles/find_object.dir/ObjWidget.cpp.o CMakeFiles/find_object.dir/ImageDropWidget.cpp.o CMakeFiles/find_object.dir/FindObject.cpp.o CMakeFiles/find_object.dir/AboutDialog.cpp.o CMakeFiles/find_object.dir/TcpServer.cpp.o CMakeFiles/find_object.dir/Vocabulary.cpp.o CMakeFiles/find_object.dir/JsonWriter.cpp.o CMakeFiles/find_object.dir/utilite/ULogger.cpp.o CMakeFiles/find_object.dir/utilite/UPlot.cpp.o CMakeFiles/find_object.dir/utilite/UDirectory.cpp.o CMakeFiles/find_object.dir/utilite/UFile.cpp.o CMakeFiles/find_object.dir/utilite/UConversion.cpp.o CMakeFiles/find_object.dir/rtabmap/PdfPlot.cpp.o CMakeFiles/find_object.dir/json/jsoncpp.cpp.o CMakeFiles/find_object.dir/Compression.cpp.o CMakeFiles/find_object.dir/__/include/find_object/moc_MainWindow.cpp.o CMakeFiles/find_object.dir/__/include/find_object/moc_FindObject.cpp.o CMakeFiles/find_object.dir/__/include/find_object/moc_Camera.cpp.o CMakeFiles/find_object.dir/__/include/find_object/moc_TcpServer.cpp.o CMakeFiles/find_object.dir/__/include/find_object/moc_ObjWidget.cpp.o CMakeFiles/find_object.dir/moc_AddObjectDialog.cpp.o CMakeFiles/find_object.dir/moc_CameraTcpServer.cpp.o CMakeFiles/find_object.dir/moc_ParametersToolBox.cpp.o CMakeFiles/find_object.dir/moc_AboutDialog.cpp.o CMakeFiles/find_object.dir/moc_RectItem.cpp.o CMakeFiles/find_object.dir/moc_ImageDropWidget.cpp.o CMakeFiles/find_object.dir/rtabmap/moc_PdfPlot.cpp.o CMakeFiles/find_object.dir/utilite/moc_UPlot.cpp.o CMakeFiles/find_object.dir/ros/moc_CameraROS.cpp.o CMakeFiles/find_object.dir/ros/moc_FindObjectROS.cpp.o CMakeFiles/find_object.dir/qrc_resources.cpp.o CMakeFiles/find_object.dir/ros/CameraROS.cpp.o CMakeFiles/find_object.dir/ros/FindObjectROS.cpp.o  -Wl,-rpath,/opt/ros/melodic/lib: /usr/lib/x86_64-linux-gnu/libopencv_shape.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_stitching.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_superres.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_videostab.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_aruco.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_datasets.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_dpm.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_face.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_freetype.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_hdf.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_optflow.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_plot.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_reg.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_saliency.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_stereo.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_text.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.3.2.0 -lz /opt/ros/melodic/lib/libcv_bridge.so /usr/lib/x86_64-linux-gnu/libopencv_core.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.3.2.0 /opt/ros/melodic/lib/libimage_transport.so /opt/ros/melodic/lib/libclass_loader.so -lPocoFoundation -ldl /opt/ros/melodic/lib/libroslib.so /opt/ros/melodic/lib/librospack.so -lpython2.7 -lboost_program_options -ltinyxml2 /opt/ros/melodic/lib/libtf.so /opt/ros/melodic/lib/libtf2_ros.so /opt/ros/melodic/lib/libactionlib.so /opt/ros/melodic/lib/libmessage_filters.so /opt/ros/melodic/lib/libroscpp.so -lboost_filesystem /opt/ros/melodic/lib/libxmlrpcpp.so /opt/ros/melodic/lib/libtf2.so /opt/ros/melodic/lib/libroscpp_serialization.so /opt/ros/melodic/lib/librosconsole.so /opt/ros/melodic/lib/librosconsole_log4cxx.so /opt/ros/melodic/lib/librosconsole_backend_interface.so -llog4cxx -lboost_regex /opt/ros/melodic/lib/librostime.so /opt/ros/melodic/lib/libcpp_common.so -lboost_system -lboost_thread -lboost_chrono -lboost_date_time -lboost_atomic -lpthread /usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4 /usr/lib/x86_64-linux-gnu/libQt5Network.so.5.9.5 /usr/lib/x86_64-linux-gnu/libQt5PrintSupport.so.5.9.5 /usr/lib/x86_64-linux-gnu/libopencv_video.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_viz.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_features2d.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_flann.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_ml.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_highgui.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_photo.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_videoio.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.3.2.0 /usr/lib/x86_64-linux-gnu/libopencv_core.so.3.2.0 /usr/lib/x86_64-linux-gnu/libQt5Widgets.so.5.9.5 /usr/lib/x86_64-linux-gnu/libQt5Gui.so.5.9.5 /usr/lib/x86_64-linux-gnu/libQt5Core.so.5.9.5 
